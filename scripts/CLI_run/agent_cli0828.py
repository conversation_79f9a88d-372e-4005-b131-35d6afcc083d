import logging
import os
import sys

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from dotenv import load_dotenv

from scripts.langchain_project.agents.recipe_agent import build_agent
from scripts.database.ingredient_utils import build_ingredient_set_from_json

# 新增 Braintrust 的導入
import braintrust as bt
# 由於 Braintrust 會處理 API 金鑰，你不需要手動處理
# os.environ.setdefault("USE_OPENAI", "false")

load_dotenv()

logging.basicConfig(level=logging.INFO, format="%(message)s")


def create_agent():
    """
    初始化並返回 LangChain Agent
    """
    # ★ 將路徑替換成你的 JSON 檔案路徑
    json_file_path = "scripts/Processing data/processed_recipes_full.json"
    build_ingredient_set_from_json(json_file_path)
    agent = build_agent()
    return agent


def main():
    agent = create_agent()

    print("RAG 智能代理（輸入 exit 離開）")
    
    while True:
        q = input("\n請描述你有的食材或需求: ").strip()
        if q.lower() in ("exit", "quit"):
            break

        try:
            # 你只需要在 agent.invoke() 這裡加上 with 區塊
            # 並指定一個實驗名稱，方便你之後在 Braintrust 網站上識別
            with bt.init(project="食譜代理專案") as experiment:
                ans = agent.invoke(q)
                output = str(ans)
                print("\n" + output)
        except Exception as e:
            error_message = f"代理程式執行錯誤：{str(e)}"
            print("\n" + error_message)


if __name__ == "__main__":
    main()